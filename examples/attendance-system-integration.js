/**
 * Integration Example - Sử dụng các component mới của hệ thống điểm danh
 * V<PERSON> dụ thực tế về cách tích hợp và sử dụng các service đã cải tiến
 */

const AttendanceOverviewService = require('../lib/services/attendanceOverviewService');
const AttendanceStatusManager = require('../lib/services/attendanceStatusManager');
const AttendanceConflictResolver = require('../lib/services/attendanceConflictResolver');
const AttendanceDataValidator = require('../lib/services/attendanceDataValidator');

class AttendanceSystemIntegrationExample {
  constructor() {
    this.conflictResolver = new AttendanceConflictResolver();
    this.dataValidator = new AttendanceDataValidator();
  }

  /**
   * Ví dụ 1: Lấy tổng quan điểm danh với conflict resolution
   */
  async example1_GetOverviewWithConflictResolution() {
    console.log('\n=== VÍ DỤ 1: Tổng quan điểm danh với Conflict Resolution ===');
    
    const userId = 'user123';
    const filters = {
      startDate: '20-08-2025',
      endDate: '25-08-2025'
    };

    const processingOptions = {
      enableConflictResolution: true,
      enableValidation: true,
      priorityRule: 'sudden_priority',
      autoFix: true,
      includeMetadata: true
    };

    try {
      const result = await AttendanceOverviewService.getAttendanceStatusOverviewV2(
        userId, 
        filters, 
        processingOptions
      );

      if (result.success) {
        console.log('✅ Lấy tổng quan thành công');
        console.log(`📊 Tổng số điểm danh: ${result.data.attendance.length}`);
        console.log(`📈 Tỷ lệ hoàn thành: ${result.data.statistics.rates.completionRate}%`);
        console.log(`⏰ Tỷ lệ đúng giờ: ${result.data.statistics.rates.onTimeRate}%`);
        
        if (result.data.metadata.conflicts) {
          console.log(`⚠️  Conflicts đã resolve: ${result.data.metadata.conflicts.conflictGroups}`);
        }
        
        if (result.data.metadata.validation) {
          console.log(`🔧 Dữ liệu đã fix: ${result.data.metadata.validation.fixedCount}`);
        }
      } else {
        console.error('❌ Lỗi:', result.error);
      }
    } catch (error) {
      console.error('❌ Exception:', error.message);
    }
  }

  /**
   * Ví dụ 2: Xử lý conflicts thủ công
   */
  async example2_ManualConflictResolution() {
    console.log('\n=== VÍ DỤ 2: Xử lý Conflicts Thủ Công ===');

    // Dữ liệu mẫu có conflicts
    const regularData = [
      {
        type: 'regular',
        date: '23-08-2025',
        shift: 'morning',
        startTime: 1724400000000, // 8:00 AM
        status: 'on_time',
        checkinTime: 1724400300000,
        createdAt: 1724400000000
      }
    ];

    const suddenData = [
      {
        type: 'sudden',
        date: '23-08-2025',
        startTime: 1724401800000, // 8:30 AM (conflict)
        status: 'late',
        checkinTime: 1724401900000,
        createdAt: 1724401800000,
        sessionId: 'session123'
      }
    ];

    // Phân tích conflicts
    const analysis = this.conflictResolver.analyzeConflicts(regularData, suddenData);
    console.log('📊 Phân tích conflicts:');
    console.log(`   - Tổng items: ${analysis.totalItems}`);
    console.log(`   - Conflict groups: ${analysis.conflictGroups}`);
    console.log(`   - Conflict rate: ${analysis.conflictRate}%`);

    // Test các priority rules khác nhau
    const rules = ['latest_wins', 'sudden_priority', 'status_priority'];
    
    for (const rule of rules) {
      console.log(`\n🔧 Testing rule: ${rule}`);
      
      const mergedData = this.conflictResolver.mergeWithPriority(regularData, suddenData, {
        priorityRule: rule,
        conflictCallback: (conflictInfo) => {
          console.log(`   ⚡ Resolved conflict: ${conflictInfo.key} using ${rule}`);
        }
      });

      console.log(`   📝 Result: ${mergedData.length} items, winner: ${mergedData[0].type}`);
    }
  }

  /**
   * Ví dụ 3: Data validation và auto-fix
   */
  async example3_DataValidationAndFix() {
    console.log('\n=== VÍ DỤ 3: Data Validation và Auto-Fix ===');

    // Dữ liệu có lỗi
    const problematicData = [
      {
        type: 'regular',
        date: '2025-08-23', // Wrong format
        shift: 'morning',
        startTime: 1724400000000
        // Missing status
      },
      {
        type: 'regular',
        date: '23-08-2025',
        shift: 'invalid_shift', // Invalid shift
        startTime: 1724400000000,
        status: 'invalid_status', // Invalid status
        checkinTime: 1724399000000 // Before startTime
      },
      {
        type: 'sudden',
        date: '23-08-2025',
        startTime: 1724414400000,
        status: 'on_time'
        // Missing sessionId
      }
    ];

    console.log('🔍 Validating problematic data...');
    
    const validationResult = this.dataValidator.validateAndFix(problematicData, {
      autoFix: true,
      strictMode: false
    });

    console.log('📊 Validation Results:');
    console.log(`   - Total items: ${validationResult.statistics.total}`);
    console.log(`   - Valid items: ${validationResult.statistics.valid}`);
    console.log(`   - Fixed items: ${validationResult.statistics.fixed}`);
    console.log(`   - Invalid items: ${validationResult.statistics.invalid}`);
    console.log(`   - Errors found: ${validationResult.errors.length}`);
    console.log(`   - Warnings: ${validationResult.warnings.length}`);

    // Generate detailed report
    const report = this.dataValidator.generateValidationReport(validationResult);
    console.log(`\n📈 Success Rate: ${report.summary.successRate}%`);
    
    console.log('\n🔧 Error Breakdown:');
    Object.entries(report.errorBreakdown).forEach(([type, count]) => {
      console.log(`   - ${type}: ${count}`);
    });

    console.log('\n✅ Fix Summary:');
    Object.entries(report.fixSummary).forEach(([type, count]) => {
      console.log(`   - ${type}: ${count} fixes applied`);
    });

    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => {
        console.log(`   - ${rec}`);
      });
    }
  }

  /**
   * Ví dụ 4: Atomic status updates
   */
  async example4_AtomicStatusUpdates() {
    console.log('\n=== VÍ DỤ 4: Atomic Status Updates ===');

    // Simulate multiple status updates
    const updates = [
      {
        type: 'workSchedule',
        id: 'schedule123',
        status: 'completed',
        metadata: { 
          updatedBy: 'system',
          reason: 'Auto-sync after shift completion'
        }
      },
      {
        type: 'attendanceRecord',
        id: 'record456',
        status: 'late',
        metadata: {
          updatedBy: 'system',
          checkinTime: Date.now()
        }
      },
      {
        type: 'suddenSession',
        id: 'session789',
        status: 'completed',
        metadata: {
          completedAt: Date.now(),
          totalCheckedIn: 25
        }
      }
    ];

    console.log(`🔄 Performing atomic update for ${updates.length} items...`);

    try {
      const result = await AttendanceStatusManager.updateStatusAtomic(updates);
      
      if (result.success) {
        console.log('✅ Atomic update successful');
        console.log(`📝 Updated ${result.data.updatedCount} items`);
        console.log('🔒 All changes committed in single transaction');
      } else {
        console.error('❌ Atomic update failed:', result.message);
        console.log('🔄 All changes rolled back');
      }
    } catch (error) {
      console.error('❌ Exception during atomic update:', error.message);
    }
  }

  /**
   * Ví dụ 5: Real-time event handling
   */
  async example5_RealTimeEventHandling() {
    console.log('\n=== VÍ DỤ 5: Real-time Event Handling ===');

    const events = [
      {
        eventType: 'attendance_checkin',
        data: {
          userId: 'user123',
          sessionId: 'session456',
          timestamp: Date.now(),
          location: { lat: 21.0285, lng: 105.8542 }
        }
      },
      {
        eventType: 'schedule_updated',
        data: {
          scheduleId: 'schedule789',
          changes: { status: 'excused', reason: 'Approved leave' }
        }
      },
      {
        eventType: 'sudden_session_created',
        data: {
          sessionId: 'session101',
          title: 'Emergency Meeting Attendance',
          targetUsers: ['user123', 'user456', 'user789']
        }
      }
    ];

    for (const event of events) {
      console.log(`\n⚡ Processing event: ${event.eventType}`);
      
      try {
        const result = await AttendanceStatusManager.syncStatusRealtime(event);
        
        if (result.success) {
          console.log(`   ✅ ${result.message}`);
        } else {
          console.log(`   ❌ ${result.message}`);
        }
      } catch (error) {
        console.error(`   ❌ Error processing event: ${error.message}`);
      }
    }
  }

  /**
   * Ví dụ 6: Performance comparison
   */
  async example6_PerformanceComparison() {
    console.log('\n=== VÍ DỤ 6: Performance Comparison ===');

    // Generate test data
    const regularData = this.generateTestData('regular', 500);
    const suddenData = this.generateTestData('sudden', 200);
    const dateRange = { startDate: '20-08-2025', endDate: '25-08-2025' };

    console.log(`📊 Test data: ${regularData.length} regular + ${suddenData.length} sudden`);

    // Test old method (simple merge)
    console.log('\n⏱️  Testing simple merge...');
    const startTime1 = Date.now();
    
    const simpleResult = AttendanceOverviewService.combineAttendanceData(
      regularData, 
      suddenData, 
      dateRange,
      {
        enableConflictResolution: false,
        enableValidation: false,
        includeMetadata: false
      }
    );
    
    const duration1 = Date.now() - startTime1;
    console.log(`   ⚡ Simple merge: ${duration1}ms`);
    console.log(`   📝 Result: ${simpleResult.attendance.length} items`);

    // Test new method (full features)
    console.log('\n⏱️  Testing enhanced merge...');
    const startTime2 = Date.now();
    
    const enhancedResult = AttendanceOverviewService.combineAttendanceData(
      regularData, 
      suddenData, 
      dateRange,
      {
        enableConflictResolution: true,
        enableValidation: true,
        priorityRule: 'sudden_priority',
        autoFix: true,
        includeMetadata: true
      }
    );
    
    const duration2 = Date.now() - startTime2;
    console.log(`   ⚡ Enhanced merge: ${duration2}ms`);
    console.log(`   📝 Result: ${enhancedResult.attendance.length} items`);
    console.log(`   🔧 Conflicts resolved: ${enhancedResult.metadata.conflicts?.conflictGroups || 0}`);
    console.log(`   ✅ Data fixed: ${enhancedResult.metadata.validation?.fixedCount || 0}`);

    const overhead = ((duration2 - duration1) / duration1 * 100).toFixed(1);
    console.log(`\n📈 Performance overhead: ${overhead}% for enhanced features`);
  }

  /**
   * Generate test data
   */
  generateTestData(type, count) {
    const data = [];
    const baseTime = new Date('2025-08-23T08:00:00').getTime();
    
    for (let i = 0; i < count; i++) {
      const item = {
        type,
        date: '23-08-2025',
        startTime: baseTime + (i * 30 * 60 * 1000), // 30 minutes apart
        status: ['on_time', 'late', 'pending'][i % 3],
        createdAt: baseTime + (i * 1000)
      };

      if (type === 'regular') {
        item.shift = i % 2 === 0 ? 'morning' : 'afternoon';
      } else {
        item.sessionId = `session${i}`;
      }

      data.push(item);
    }
    
    return data;
  }

  /**
   * Chạy tất cả examples
   */
  async runAllExamples() {
    console.log('🚀 ATTENDANCE SYSTEM INTEGRATION EXAMPLES');
    console.log('==========================================');

    try {
      await this.example1_GetOverviewWithConflictResolution();
      await this.example2_ManualConflictResolution();
      await this.example3_DataValidationAndFix();
      await this.example4_AtomicStatusUpdates();
      await this.example5_RealTimeEventHandling();
      await this.example6_PerformanceComparison();

      console.log('\n🎉 All examples completed successfully!');
    } catch (error) {
      console.error('\n💥 Error running examples:', error.message);
    }
  }
}

// Export for use in other files
module.exports = AttendanceSystemIntegrationExample;

// Run examples if this file is executed directly
if (require.main === module) {
  const example = new AttendanceSystemIntegrationExample();
  example.runAllExamples();
}
