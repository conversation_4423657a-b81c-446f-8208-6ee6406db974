const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const suddenAttendanceService = require('../../../../services/suddenAttendanceService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API thống kê chấm công đột xuất
 * POST /api/v1.0/admin/sudden-attendance/statistics
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    sessionId,
    startDate,
    endDate,
    status,
    createdBy,
    attendanceStatus
  } = req.body;

  let result;

  const validateParams = (next) => {
    // Valid sudden attendance status values
    const validAttendanceStatuses = ['on_time', 'late', 'absent', 'pending', 'exempted'];

    const schema = Joi.object({
      sessionId: Joi.objectId().optional(),
      startDate: Joi.string().optional(),
      endDate: Joi.string().optional(),
      status: Joi.string().valid('scheduled', 'active', 'completed', 'cancelled').optional(),
      createdBy: Joi.objectId().optional(),
      attendanceStatus: Joi.alternatives().try(
        Joi.string().valid(...validAttendanceStatuses),
        Joi.array().items(Joi.string().valid(...validAttendanceStatuses)),
        Joi.string().custom((value, helpers) => {
          // Handle comma-separated values
          const statuses = value.split(',').map(s => s.trim());
          const invalidStatuses = statuses.filter(s => !validAttendanceStatuses.includes(s));
          if (invalidStatuses.length > 0) {
            return helpers.error('any.invalid');
          }
          return statuses;
        })
      ).optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getStatistics = (next) => {
    try {
      let statistics;

      if (sessionId) {
        // Thống kê chi tiết cho một phiên cụ thể
        statistics = suddenAttendanceService.getSessionDetailStatistics(sessionId);
      } else {
        // Thống kê tổng quan
        statistics = suddenAttendanceService.getOverallStatistics({
          startDate,
          endDate,
          status,
          createdBy,
          userId
        });
      }

      statistics
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          // Apply attendance status filter if provided and we have session detail data
          let filteredData = res.data;
          if (attendanceStatus && sessionId && res.data && res.data.records) {
            filteredData = {
              ...res.data,
              records: filterByAttendanceStatus(res.data.records, attendanceStatus, res.data.session)
            };
          }

          result = { ...res, data: filteredData };
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Filter sudden attendance records by attendance status
   * @param {Array} records - Array of attendance records
   * @param {String|Array} statusFilter - Status filter (single value, array, or comma-separated string)
   * @param {Object} session - Session information
   * @returns {Array} Filtered records
   */
  const filterByAttendanceStatus = (records, statusFilter, session) => {
    if (!records || !Array.isArray(records)) {
      return records;
    }

    // Normalize status filter to array
    let statusArray = [];
    if (typeof statusFilter === 'string') {
      if (statusFilter.includes(',')) {
        statusArray = statusFilter.split(',').map(s => s.trim());
      } else {
        statusArray = [statusFilter];
      }
    } else if (Array.isArray(statusFilter)) {
      statusArray = statusFilter;
    } else {
      return records; // Invalid filter, return all
    }

    // Filter records based on attendance status
    return records.filter(record => {
      // Determine the status of this record
      let recordStatus = 'absent'; // Default for users who didn't check in

      if (record.status) {
        recordStatus = record.status; // 'on_time' or 'late'
      } else {
        // Check if user is exempted
        if (session.exemptedUsers && session.exemptedUsers.some(exempt =>
          exempt.user.toString() === record.user._id.toString())) {
          recordStatus = 'exempted';
        } else {
          // Check if session is still active for pending status
          const now = Date.now();
          const sessionEndTime = session.startTime + (session.validDurationMinutes * 60 * 1000);
          if (now <= sessionEndTime) {
            recordStatus = 'pending';
          }
        }
      }

      return statusArray.includes(recordStatus);
    });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });

    // Log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'get_sudden_attendance_stats',
        description: 'Xem thống kê chấm công đột xuất',
        data: req.body,
        updatedData: result.data
      }, () => {});
    }
  };

  async.waterfall([
    validateParams,
    getStatistics,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
