/**
 * AttendanceConflictResolver - Component xử lý xung đột dữ liệu điểm danh
 * Gi<PERSON>i quyết conflicts khi có trùng lặp giữa regular và sudden attendance
 * Áp dụng các quy tắc ưu tiên để đảm bảo tính nhất quán
 */

const DateUtils = require('../utils/dateUtils');

class AttendanceConflictResolver {
  constructor() {
    // Định nghĩa các quy tắc ưu tiên
    this.PRIORITY_RULES = {
      LATEST_WINS: 'latest_wins',           // Dữ liệu mới nhất thắng
      SUDDEN_PRIORITY: 'sudden_priority',   // Sudden attendance ưu tiên
      MANUAL_PRIORITY: 'manual_priority',   // Ưu tiên theo cấu hình thủ công
      STATUS_PRIORITY: 'status_priority'    // Ưu tiên theo trạng thái
    };

    // Thứ tự ưu tiên trạng thái (cao -> thấp)
    this.STATUS_PRIORITY_ORDER = [
      'on_time',
      'late', 
      'excused',
      'business_trip',
      'absent',
      'missed',
      'pending',
      'scheduled'
    ];
  }

  /**
   * Merge dữ liệu với xử lý conflict
   * @param {Array} regularData - Dữ liệu điểm danh thường
   * @param {Array} suddenData - Dữ liệu điểm danh đột xuất
   * @param {Object} options - Tùy chọn merge
   * @returns {Array} Dữ liệu đã merge và resolve conflicts
   */
  mergeWithPriority(regularData, suddenData, options = {}) {
    const { 
      priorityRule = this.PRIORITY_RULES.LATEST_WINS,
      preserveOriginal = false,
      conflictCallback = null
    } = options;

    try {
      // Tạo map để detect conflicts
      const conflictMap = this._buildConflictMap(regularData, suddenData);
      
      // Xử lý conflicts
      const resolvedData = this._resolveConflicts(
        conflictMap, 
        priorityRule, 
        conflictCallback
      );

      // Thêm metadata về conflict resolution
      const result = resolvedData.map(item => ({
        ...item,
        _metadata: {
          hasConflict: item._hasConflict || false,
          resolvedBy: item._resolvedBy || 'no_conflict',
          originalCount: item._originalCount || 1,
          resolvedAt: Date.now()
        }
      }));

      // Cleanup temporary fields
      result.forEach(item => {
        delete item._hasConflict;
        delete item._resolvedBy;
        delete item._originalCount;
      });

      return result;

    } catch (error) {
      console.error('[AttendanceConflictResolver] Error in mergeWithPriority:', error);
      // Fallback: simple merge without conflict resolution
      return [...regularData, ...suddenData];
    }
  }

  /**
   * Xây dựng conflict map để detect trùng lặp
   * @private
   */
  _buildConflictMap(regularData, suddenData) {
    const conflictMap = new Map();

    // Process regular data
    regularData.forEach(item => {
      const key = this._generateConflictKey(item);
      if (!conflictMap.has(key)) {
        conflictMap.set(key, []);
      }
      conflictMap.get(key).push({
        ...item,
        _sourceType: 'regular'
      });
    });

    // Process sudden data
    suddenData.forEach(item => {
      const key = this._generateConflictKey(item);
      if (!conflictMap.has(key)) {
        conflictMap.set(key, []);
      }
      conflictMap.get(key).push({
        ...item,
        _sourceType: 'sudden'
      });
    });

    return conflictMap;
  }

  /**
   * Tạo key để detect conflict (cùng ngày, cùng thời gian gần nhau)
   * @private
   */
  _generateConflictKey(item) {
    const { date, startTime } = item;
    
    // Làm tròn thời gian xuống 30 phút để detect conflicts
    const roundedTime = Math.floor(startTime / (30 * 60 * 1000)) * (30 * 60 * 1000);
    
    return `${date}_${roundedTime}`;
  }

  /**
   * Resolve conflicts dựa trên priority rule
   * @private
   */
  _resolveConflicts(conflictMap, priorityRule, conflictCallback) {
    const resolvedData = [];

    for (const [key, items] of conflictMap) {
      if (items.length === 1) {
        // Không có conflict
        resolvedData.push(items[0]);
      } else {
        // Có conflict, cần resolve
        const resolved = this._resolveConflictGroup(items, priorityRule);
        
        // Gọi callback nếu có
        if (conflictCallback) {
          try {
            conflictCallback({
              key,
              originalItems: items,
              resolvedItem: resolved,
              rule: priorityRule
            });
          } catch (error) {
            console.error('[AttendanceConflictResolver] Callback error:', error);
          }
        }

        resolvedData.push(resolved);
      }
    }

    return resolvedData;
  }

  /**
   * Resolve một nhóm conflicts
   * @private
   */
  _resolveConflictGroup(items, priorityRule) {
    switch (priorityRule) {
      case this.PRIORITY_RULES.LATEST_WINS:
        return this._resolveByLatest(items);
      
      case this.PRIORITY_RULES.SUDDEN_PRIORITY:
        return this._resolveBySuddenPriority(items);
      
      case this.PRIORITY_RULES.STATUS_PRIORITY:
        return this._resolveByStatusPriority(items);
      
      case this.PRIORITY_RULES.MANUAL_PRIORITY:
        return this._resolveByManualPriority(items);
      
      default:
        return this._resolveByLatest(items);
    }
  }

  /**
   * Resolve bằng cách chọn item mới nhất
   * @private
   */
  _resolveByLatest(items) {
    const latest = items.reduce((prev, current) => {
      const prevTime = prev.checkinTime || prev.createdAt || 0;
      const currentTime = current.checkinTime || current.createdAt || 0;
      return currentTime > prevTime ? current : prev;
    });

    return {
      ...latest,
      _hasConflict: true,
      _resolvedBy: this.PRIORITY_RULES.LATEST_WINS,
      _originalCount: items.length
    };
  }

  /**
   * Resolve bằng cách ưu tiên sudden attendance
   * @private
   */
  _resolveBySuddenPriority(items) {
    const suddenItem = items.find(item => item._sourceType === 'sudden');
    const winner = suddenItem || items[0];

    return {
      ...winner,
      _hasConflict: true,
      _resolvedBy: this.PRIORITY_RULES.SUDDEN_PRIORITY,
      _originalCount: items.length
    };
  }

  /**
   * Resolve bằng cách ưu tiên theo trạng thái
   * @private
   */
  _resolveByStatusPriority(items) {
    const winner = items.reduce((prev, current) => {
      const prevPriority = this.STATUS_PRIORITY_ORDER.indexOf(prev.status);
      const currentPriority = this.STATUS_PRIORITY_ORDER.indexOf(current.status);
      
      // Nếu không tìm thấy trong priority order, đặt ở cuối
      const prevIndex = prevPriority === -1 ? 999 : prevPriority;
      const currentIndex = currentPriority === -1 ? 999 : currentPriority;
      
      return currentIndex < prevIndex ? current : prev;
    });

    return {
      ...winner,
      _hasConflict: true,
      _resolvedBy: this.PRIORITY_RULES.STATUS_PRIORITY,
      _originalCount: items.length
    };
  }

  /**
   * Resolve bằng cách ưu tiên thủ công (có thể customize)
   * @private
   */
  _resolveByManualPriority(items) {
    // TODO: Implement custom manual priority logic
    // Hiện tại fallback về latest wins
    return this._resolveByLatest(items);
  }

  /**
   * Phân tích conflicts trong dữ liệu
   * @param {Array} regularData - Dữ liệu điểm danh thường
   * @param {Array} suddenData - Dữ liệu điểm danh đột xuất
   * @returns {Object} Báo cáo phân tích conflicts
   */
  analyzeConflicts(regularData, suddenData) {
    try {
      const conflictMap = this._buildConflictMap(regularData, suddenData);
      
      const analysis = {
        totalItems: regularData.length + suddenData.length,
        regularCount: regularData.length,
        suddenCount: suddenData.length,
        conflictGroups: 0,
        conflictItems: 0,
        conflictDetails: []
      };

      for (const [key, items] of conflictMap) {
        if (items.length > 1) {
          analysis.conflictGroups++;
          analysis.conflictItems += items.length;
          
          analysis.conflictDetails.push({
            key,
            itemCount: items.length,
            types: items.map(item => item._sourceType),
            statuses: items.map(item => item.status),
            dates: [...new Set(items.map(item => item.date))],
            timeRange: {
              min: Math.min(...items.map(item => item.startTime)),
              max: Math.max(...items.map(item => item.startTime))
            }
          });
        }
      }

      analysis.conflictRate = analysis.totalItems > 0 
        ? Math.round((analysis.conflictItems / analysis.totalItems) * 100) 
        : 0;

      return analysis;

    } catch (error) {
      console.error('[AttendanceConflictResolver] Error in analyzeConflicts:', error);
      return {
        error: error.message,
        totalItems: regularData.length + suddenData.length,
        conflictGroups: 0,
        conflictItems: 0
      };
    }
  }

  /**
   * Validate conflict resolution rules
   * @param {String} rule - Rule để validate
   * @returns {Boolean} Rule có hợp lệ không
   */
  validatePriorityRule(rule) {
    return Object.values(this.PRIORITY_RULES).includes(rule);
  }

  /**
   * Lấy danh sách các priority rules có sẵn
   * @returns {Array} Danh sách rules
   */
  getAvailablePriorityRules() {
    return Object.entries(this.PRIORITY_RULES).map(([key, value]) => ({
      key,
      value,
      description: this._getRuleDescription(value)
    }));
  }

  /**
   * Lấy mô tả cho priority rule
   * @private
   */
  _getRuleDescription(rule) {
    const descriptions = {
      [this.PRIORITY_RULES.LATEST_WINS]: 'Dữ liệu mới nhất sẽ được ưu tiên',
      [this.PRIORITY_RULES.SUDDEN_PRIORITY]: 'Điểm danh đột xuất được ưu tiên',
      [this.PRIORITY_RULES.STATUS_PRIORITY]: 'Ưu tiên theo thứ tự trạng thái',
      [this.PRIORITY_RULES.MANUAL_PRIORITY]: 'Ưu tiên theo cấu hình thủ công'
    };

    return descriptions[rule] || 'Không có mô tả';
  }
}

module.exports = AttendanceConflictResolver;
