/**
 * AttendanceStatusManager - Service tập trung quản lý trạng thái điểm danh
 * Quản lý trạng thái thống nhất cho cả regular và sudden attendance
 * Đ<PERSON><PERSON> bảo tính nhất quán và đồng bộ dữ liệu
 */

const mongoose = require('mongoose');
const WorkSchedule = require('../models/workSchedule');
const AttendanceRecord = require('../models/attendanceRecord');
const SuddenAttendanceSession = require('../models/suddenAttendanceSession');
const SuddenAttendanceRecord = require('../models/suddenAttendanceRecord');
const DateUtils = require('../utils/dateUtils');
const CONSTANTS = require('../const');

class AttendanceStatusManager {
  constructor() {
    // Định nghĩa priority rules cho conflict resolution
    this.PRIORITY_RULES = {
      LATEST_WINS: 'latest_wins',
      SUDDEN_PRIORITY: 'sudden_priority',
      MANUAL_PRIORITY: 'manual_priority'
    };

    // Mapping trạng thái thống nhất
    this.UNIFIED_STATUS = {
      // Trạng thái hoàn thành
      ON_TIME: 'on_time',
      LATE: 'late',
      
      // Trạng thái vắng mặt
      MISSED: 'missed',
      ABSENT: 'absent',
      
      // Trạng thái miễn điểm danh
      EXCUSED: 'excused',
      BUSINESS_TRIP: 'business_trip',
      
      // Trạng thái chờ xử lý
      PENDING: 'pending',
      SCHEDULED: 'scheduled'
    };
  }

  /**
   * Xác định trạng thái thống nhất cho một attendance item
   * @param {Object} attendanceItem - Item điểm danh (regular hoặc sudden)
   * @param {Object} options - Tùy chọn xử lý
   * @returns {String} Trạng thái thống nhất
   */
  determineUnifiedStatus(attendanceItem, options = {}) {
    const { type, shift, record, session, exemption, date } = attendanceItem;
    const { currentTime = Date.now() } = options;

    try {
      // Xử lý trạng thái miễn điểm danh trước
      if (this._isExempted(attendanceItem)) {
        return this._getExemptionStatus(attendanceItem);
      }

      // Xử lý theo loại điểm danh
      if (type === 'regular') {
        return this._determineRegularStatus(shift, record, date, currentTime);
      } else if (type === 'sudden') {
        return this._determineSuddenStatus(session, record, currentTime);
      }

      // Fallback
      return this.UNIFIED_STATUS.SCHEDULED;

    } catch (error) {
      console.error('[AttendanceStatusManager] Error determining status:', error);
      return this.UNIFIED_STATUS.SCHEDULED;
    }
  }

  /**
   * Xác định trạng thái điểm danh thường
   * @private
   */
  _determineRegularStatus(shift, record, date, currentTime) {
    // Nếu có bản ghi điểm danh thực tế
    if (record) {
      return record.status; // 'on_time' hoặc 'late'
    }

    // Nếu shift đã được đánh dấu trạng thái đặc biệt
    if (shift && shift.status) {
      if (['excused', 'business_trip'].includes(shift.status)) {
        return shift.status;
      }
      if (shift.status === 'missed') {
        return this.UNIFIED_STATUS.MISSED;
      }
    }

    // Xác định trạng thái dựa trên thời gian
    if (date && this._isShiftCompleted(date, shift.type, currentTime)) {
      return this.UNIFIED_STATUS.MISSED;
    }

    return this.UNIFIED_STATUS.PENDING;
  }

  /**
   * Xác định trạng thái điểm danh đột xuất
   * @private
   */
  _determineSuddenStatus(session, record, currentTime) {
    // Nếu có bản ghi điểm danh thực tế
    if (record) {
      return record.status; // 'on_time', 'late', hoặc 'absent'
    }

    // Xác định trạng thái dựa trên thời gian session
    const sessionEndTime = session.startTime + (session.validDurationMinutes * 60 * 1000);
    const lateEndTime = session.startTime + (60 * 60 * 1000); // 1 giờ sau giờ bắt đầu

    if (currentTime > lateEndTime) {
      return this.UNIFIED_STATUS.ABSENT;
    } else if (currentTime > sessionEndTime) {
      return this.UNIFIED_STATUS.PENDING; // Có thể chấm công muộn
    } else {
      return this.UNIFIED_STATUS.PENDING; // Đang trong thời gian chấm công
    }
  }

  /**
   * Kiểm tra có được miễn điểm danh không
   * @private
   */
  _isExempted(attendanceItem) {
    const { shift, exemption, type } = attendanceItem;
    
    if (exemption) {
      return true;
    }

    if (type === 'regular' && shift) {
      return ['excused', 'business_trip'].includes(shift.status);
    }

    return false;
  }

  /**
   * Lấy trạng thái miễn điểm danh
   * @private
   */
  _getExemptionStatus(attendanceItem) {
    const { shift, exemption } = attendanceItem;

    if (exemption) {
      return exemption.reason;
    }

    if (shift && ['excused', 'business_trip'].includes(shift.status)) {
      return shift.status;
    }

    return this.UNIFIED_STATUS.EXCUSED;
  }

  /**
   * Kiểm tra ca làm việc đã kết thúc chưa
   * @private
   */
  _isShiftCompleted(date, shiftType, currentTime) {
    const currentDate = DateUtils.getCurrentDateDDMMYYYY();
    
    // Nếu là ngày trong tương lai, chưa kết thúc
    if (DateUtils.compareDDMMYYYY(date, currentDate) > 0) {
      return false;
    }

    // Nếu là ngày trong quá khứ, đã kết thúc
    if (DateUtils.compareDDMMYYYY(date, currentDate) < 0) {
      return true;
    }

    // Nếu là ngày hôm nay, kiểm tra thời gian
    const now = new Date(currentTime);
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // Thời gian kết thúc ca làm việc
    let shiftEndTime;
    if (shiftType === 'morning') {
      shiftEndTime = 12 * 60; // 12:00
    } else if (shiftType === 'afternoon') {
      shiftEndTime = 18 * 60; // 18:00
    }

    return currentTimeInMinutes >= shiftEndTime;
  }

  /**
   * Cập nhật trạng thái atomic với MongoDB transaction
   * @param {Array} updates - Danh sách cập nhật
   * @returns {Object} Kết quả cập nhật
   */
  async updateStatusAtomic(updates) {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();
      
      const results = [];
      
      for (const update of updates) {
        const result = await this._executeUpdate(update, session);
        results.push(result);
      }
      
      await session.commitTransaction();
      
      return {
        success: true,
        message: 'Cập nhật trạng thái thành công',
        data: {
          updatedCount: results.length,
          results
        }
      };
      
    } catch (error) {
      await session.abortTransaction();
      console.error('[AttendanceStatusManager] Transaction failed:', error);
      
      return {
        success: false,
        message: 'Lỗi cập nhật trạng thái',
        error: error.message
      };
    } finally {
      session.endSession();
    }
  }

  /**
   * Thực hiện một update operation
   * @private
   */
  async _executeUpdate(update, session) {
    const { type, id, status, metadata } = update;
    
    const updateData = {
      status,
      updatedAt: Date.now(),
      ...metadata
    };
    
    if (type === 'workSchedule') {
      return await WorkSchedule.findByIdAndUpdate(id, updateData, { session, new: true });
    } else if (type === 'attendanceRecord') {
      return await AttendanceRecord.findByIdAndUpdate(id, updateData, { session, new: true });
    } else if (type === 'suddenSession') {
      return await SuddenAttendanceSession.findByIdAndUpdate(id, updateData, { session, new: true });
    } else if (type === 'suddenRecord') {
      return await SuddenAttendanceRecord.findByIdAndUpdate(id, updateData, { session, new: true });
    }
    
    throw new Error(`Unknown update type: ${type}`);
  }

  /**
   * Đồng bộ trạng thái real-time dựa trên events
   * @param {Object} eventData - Dữ liệu event
   * @returns {Object} Kết quả đồng bộ
   */
  async syncStatusRealtime(eventData) {
    try {
      const { eventType, data } = eventData;
      
      switch (eventType) {
        case 'attendance_checkin':
          return await this._handleCheckinEvent(data);
        case 'schedule_updated':
          return await this._handleScheduleUpdateEvent(data);
        case 'sudden_session_created':
          return await this._handleSuddenSessionEvent(data);
        default:
          console.warn(`[AttendanceStatusManager] Unknown event type: ${eventType}`);
          return { success: false, message: 'Unknown event type' };
      }
      
    } catch (error) {
      console.error('[AttendanceStatusManager] Error in real-time sync:', error);
      return {
        success: false,
        message: 'Lỗi đồng bộ real-time',
        error: error.message
      };
    }
  }

  /**
   * Xử lý event checkin
   * @private
   */
  async _handleCheckinEvent(data) {
    // TODO: Implement checkin event handling
    return { success: true, message: 'Checkin event processed' };
  }

  /**
   * Xử lý event cập nhật lịch
   * @private
   */
  async _handleScheduleUpdateEvent(data) {
    // TODO: Implement schedule update event handling
    return { success: true, message: 'Schedule update event processed' };
  }

  /**
   * Xử lý event tạo sudden session
   * @private
   */
  async _handleSuddenSessionEvent(data) {
    // TODO: Implement sudden session event handling
    return { success: true, message: 'Sudden session event processed' };
  }
}

module.exports = new AttendanceStatusManager();
