/**
 * Service tổng hợp trạng thái điểm danh
 * Quản lý tổng quan về tất cả các loại điểm danh (thường + đột xuất)
 */

const WorkSchedule = require('../models/workSchedule');
const AttendanceRecord = require('../models/attendanceRecord');
const SuddenAttendanceSession = require('../models/suddenAttendanceSession');
const SuddenAttendanceRecord = require('../models/suddenAttendanceRecord');
const DateUtils = require('../utils/dateUtils');

// Import các service mới
const AttendanceStatusManager = require('./attendanceStatusManager');
const AttendanceConflictResolver = require('./attendanceConflictResolver');
const AttendanceDataValidator = require('./attendanceDataValidator');

class AttendanceOverviewService {
  /**
   * <PERSON><PERSON>y tổng quan trạng thái điểm danh cho cán bộ
   * @param {String} userId - ID cán bộ
   * @param {Object} filters - <PERSON><PERSON> lọc (date, startDate, endDate, period)
   * @returns {Object} Tổng quan trạng thái điểm danh
   */
  async getAttendanceStatusOverview(userId, filters = {}) {
    try {
      const {
        date,
        startDate,
        endDate,
        period = 'day' // day, week, month
      } = filters;

      // Xác định khoảng thời gian
      const dateRange = this.calculateDateRange(date, startDate, endDate, period);

      // Lấy dữ liệu song song
      const [regularAttendance, suddenAttendance] = await Promise.all([
        this.getRegularAttendanceData(userId, dateRange),
        this.getSuddenAttendanceData(userId, dateRange)
      ]);

      // Tổng hợp dữ liệu
      const overview = this.combineAttendanceData(regularAttendance, suddenAttendance, dateRange);

      return {
        success: true,
        data: overview
      };

    } catch (error) {
      console.error('Error getting attendance status overview:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Tính toán khoảng thời gian dựa trên filters
   */
  calculateDateRange(date, startDate, endDate, period) {
    if (startDate && endDate) {
      return { startDate, endDate };
    }

    if (date) {
      return { startDate: date, endDate: date };
    }

    // Mặc định theo period
    const today = new Date();
    const todayStr = DateUtils.convertYYYYMMDDtoDDMMYYYY(today.toISOString().split('T')[0]);

    switch (period) {
      case 'week':
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);

        return {
          startDate: DateUtils.convertYYYYMMDDtoDDMMYYYY(startOfWeek.toISOString().split('T')[0]),
          endDate: DateUtils.convertYYYYMMDDtoDDMMYYYY(endOfWeek.toISOString().split('T')[0])
        };

      case 'month':
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

        return {
          startDate: DateUtils.convertYYYYMMDDtoDDMMYYYY(startOfMonth.toISOString().split('T')[0]),
          endDate: DateUtils.convertYYYYMMDDtoDDMMYYYY(endOfMonth.toISOString().split('T')[0])
        };

      default: // day
        return { startDate: todayStr, endDate: todayStr };
    }
  }

  /**
   * Lấy dữ liệu điểm danh thường
   */
  async getRegularAttendanceData(userId, dateRange) {
    const { startDate, endDate } = dateRange;

    // Lấy lịch làm việc
    const schedules = await WorkSchedule.find({
      user: userId,
      date: { $gte: startDate, $lte: endDate },
      status: 1
    }).lean();

    // Lấy bản ghi điểm danh
    const records = await AttendanceRecord.find({
      user: userId,
      date: { $gte: startDate, $lte: endDate }
    }).lean();

    // Map records theo date và shift
    const recordsMap = {};
    records.forEach(record => {
      const key = `${record.date}_${record.shift}`;
      recordsMap[key] = record;
    });

    // Tổng hợp dữ liệu
    const attendanceData = [];
    schedules.forEach(schedule => {
      schedule.shifts.forEach(shift => {
        const key = `${schedule.date}_${shift.type}`;
        const record = recordsMap[key];

        attendanceData.push({
          type: 'regular',
          date: schedule.date,
          shift: shift.type,
          startTime: DateUtils.convertTimeStringToTimestamp(shift.startTime),
          status: this.determineRegularAttendanceStatus(shift, record),
          checkinTime: record ? record.checkinTime : null,
          location: record ? record.location : null,
          isExempted: ['excused', 'business_trip'].includes(shift.status),
          exemptionReason: shift.status === 'business_trip' ? 'Công tác' :
                          shift.status === 'excused' ? 'Nghỉ phép' : null,
          scheduleId: schedule._id,
          recordId: record ? record._id : null
        });
      });
    });

    return attendanceData;
  }

  /**
   * Lấy dữ liệu điểm danh đột xuất
   */
  async getSuddenAttendanceData(userId, dateRange) {
    const { startDate, endDate } = dateRange;

    // Convert date range to timestamps
    const startTimestamp = new Date(DateUtils.convertDDMMYYYYtoYYYYMMDD(startDate)).getTime();
    const endTimestamp = new Date(DateUtils.convertDDMMYYYYtoYYYYMMDD(endDate)).getTime() + (24 * 60 * 60 * 1000) - 1;

    // Lấy các phiên chấm công đột xuất
    const sessions = await SuddenAttendanceSession.find({
      targetUsers: userId,
      startTime: { $gte: startTimestamp, $lte: endTimestamp }
    }).lean();

    // Lấy bản ghi chấm công đột xuất
    const sessionIds = sessions.map(s => s._id);
    const records = await SuddenAttendanceRecord.find({
      session: { $in: sessionIds },
      user: userId
    }).lean();

    // Map records theo session
    const recordsMap = {};
    records.forEach(record => {
      recordsMap[record.session.toString()] = record;
    });

    // Tổng hợp dữ liệu
    const attendanceData = [];
    sessions.forEach(session => {
      const record = recordsMap[session._id.toString()];
      const sessionDate = new Date(session.startTime);
      const dateString = DateUtils.convertYYYYMMDDtoDDMMYYYY(sessionDate.toISOString().split('T')[0]);

      // Kiểm tra xem user có được miễn điểm danh không
      const exemption = session.exemptedUsers.find(ex => ex.user.toString() === userId);

      attendanceData.push({
        type: 'sudden',
        date: dateString,
        title: session.title,
        startTime: session.startTime,
        validTime: session.startTime + (session.validDurationMinutes * 60 * 1000),
        status: this.determineSuddenAttendanceStatus(session, record, exemption),
        checkinTime: record ? record.checkinTime : null,
        location: record ? record.location : null,
        isExempted: !!exemption,
        exemptionReason: exemption ? (exemption.reason === 'business_trip' ? 'Công tác' : 'Nghỉ phép') : null,
        scheduleId: session._id,
        recordId: record ? record._id : null
      });
    });

    return attendanceData;
  }

  /**
   * Xác định trạng thái điểm danh thường
   */
  determineRegularAttendanceStatus(shift, record) {
    // if (['excused', 'business_trip'].includes(shift.status)) {
      return shift.status;
    // }

    // if (!record) {
    //   return 'missed';
    // }

    // return record.status; // 'on_time' hoặc 'late'
  }

  /**
   * Xác định trạng thái điểm danh đột xuất
   */
  determineSuddenAttendanceStatus(session, record, exemption) {
    if (exemption) {
      return exemption.reason;
    }

    if (!record) {
      const now = Date.now();
      const lateEndTime = session.startTime + (60 * 60 * 1000); // 1 giờ sau giờ bắt đầu

      if (now > lateEndTime) {
        return 'absent';
      } else {
        return 'pending';
      }
    }

    return record.status; // 'on_time' hoặc 'late'
  }

  /**
   * Tổng hợp dữ liệu điểm danh (Cải tiến với conflict resolution và validation)
   * @param {Array} regularData - Dữ liệu điểm danh thường
   * @param {Array} suddenData - Dữ liệu điểm danh đột xuất
   * @param {Object} dateRange - Khoảng thời gian
   * @param {Object} options - Tùy chọn xử lý
   * @returns {Object} Dữ liệu đã tổng hợp và validate
   */
  combineAttendanceData(regularData, suddenData, dateRange, options = {}) {
    try {
      const {
        enableConflictResolution = true,
        enableValidation = true,
        priorityRule = 'latest_wins',
        autoFix = true,
        includeMetadata = true
      } = options;

      // Bước 1: Conflict Resolution
      let mergedData = [];
      let conflictAnalysis = null;

      if (enableConflictResolution) {
        const conflictResolver = new AttendanceConflictResolver();

        // Phân tích conflicts trước khi merge
        conflictAnalysis = conflictResolver.analyzeConflicts(regularData, suddenData);

        // Merge với conflict resolution
        mergedData = conflictResolver.mergeWithPriority(regularData, suddenData, {
          priorityRule,
          preserveOriginal: false,
          conflictCallback: (conflictInfo) => {
            console.log(`[AttendanceOverview] Resolved conflict: ${conflictInfo.key}`);
          }
        });
      } else {
        // Simple merge without conflict resolution
        mergedData = [...regularData, ...suddenData];
      }

      // Bước 2: Data Validation
      let validationResult = null;
      let finalData = mergedData;

      if (enableValidation) {
        const validator = new AttendanceDataValidator();
        validationResult = validator.validateAndFix(mergedData, {
          autoFix,
          strictMode: false,
          validationRules: [
            'required_fields',
            'date_format',
            'time_logic',
            'status_consistency',
            'business_logic'
          ]
        });

        finalData = validationResult.validData;

        // Log validation issues
        if (validationResult.errors.length > 0) {
          console.warn(`[AttendanceOverview] Validation found ${validationResult.errors.length} errors`);
        }
      }

      // Bước 3: Sắp xếp dữ liệu
      finalData.sort((a, b) => {
        const dateCompare = a.date.localeCompare(b.date);
        if (dateCompare !== 0) return dateCompare;

        // So sánh thời gian
        const timeA = a.startTime || 0;
        const timeB = b.startTime || 0;
        return timeA - timeB;
      });

      // Bước 4: Tính toán statistics real-time
      const statistics = this.calculateRealTimeStatistics(finalData, dateRange);

      // Bước 5: Tạo kết quả với metadata
      const result = {
        dateRange,
        attendance: finalData,
        statistics
      };

      // Thêm metadata nếu được yêu cầu
      if (includeMetadata) {
        result.metadata = {
          processing: {
            originalCount: {
              regular: regularData.length,
              sudden: suddenData.length,
              total: regularData.length + suddenData.length
            },
            finalCount: finalData.length,
            conflictResolution: enableConflictResolution,
            validation: enableValidation,
            processedAt: Date.now()
          }
        };

        // Thêm conflict analysis nếu có
        if (conflictAnalysis) {
          result.metadata.conflicts = conflictAnalysis;
        }

        // Thêm validation result nếu có
        if (validationResult) {
          result.metadata.validation = {
            isValid: validationResult.isValid,
            statistics: validationResult.statistics,
            errorCount: validationResult.errors.length,
            warningCount: validationResult.warnings.length,
            fixedCount: validationResult.fixedItems.length
          };
        }
      }

      return result;

    } catch (error) {
      console.error('[AttendanceOverview] Error in combineAttendanceData:', error);

      // Fallback: trả về dữ liệu đơn giản
      const fallbackData = [...regularData, ...suddenData];
      fallbackData.sort((a, b) => {
        const dateCompare = a.date.localeCompare(b.date);
        if (dateCompare !== 0) return dateCompare;
        return (a.startTime || 0) - (b.startTime || 0);
      });

      return {
        dateRange,
        attendance: fallbackData,
        statistics: this.calculateBasicStatistics(fallbackData),
        metadata: {
          error: error.message,
          fallbackMode: true,
          processedAt: Date.now()
        }
      };
    }
  }

  /**
   * Tính thống kê tổng quan
   */
  calculateStatistics(attendanceData) {
    const stats = {
      total: attendanceData.length,
      regular: {
        total: 0,
        onTime: 0,
        late: 0,
        missed: 0,
        excused: 0,
        businessTrip: 0
      },
      sudden: {
        total: 0,
        onTime: 0,
        late: 0,
        absent: 0,
        pending: 0,
        excused: 0,
        businessTrip: 0
      },
      overall: {
        completed: 0,
        missed: 0,
        exempted: 0,
        pending: 0
      }
    };

    attendanceData.forEach(item => {
      if (item.type === 'regular') {
        stats.regular.total++;
        switch (item.status) {
          case 'on_time': stats.regular.onTime++; stats.overall.completed++; break;
          case 'late': stats.regular.late++; stats.overall.completed++; break;
          case 'missed': stats.regular.missed++; stats.overall.missed++; break;
          case 'excused': stats.regular.excused++; stats.overall.exempted++; break;
          case 'business_trip': stats.regular.businessTrip++; stats.overall.exempted++; break;
        }
      } else {
        stats.sudden.total++;
        switch (item.status) {
          case 'on_time': stats.sudden.onTime++; stats.overall.completed++; break;
          case 'late': stats.sudden.late++; stats.overall.completed++; break;
          case 'absent': stats.sudden.absent++; stats.overall.missed++; break;
          case 'pending': stats.sudden.pending++; stats.overall.pending++; break;
          case 'excused': stats.sudden.excused++; stats.overall.exempted++; break;
          case 'business_trip': stats.sudden.businessTrip++; stats.overall.exempted++; break;
        }
      }
    });

    return stats;
  }

  /**
   * Tính toán statistics real-time
   * @param {Array} attendanceData - Dữ liệu điểm danh
   * @param {Object} dateRange - Khoảng thời gian
   * @returns {Object} Statistics chi tiết
   */
  calculateRealTimeStatistics(attendanceData, dateRange) {
    try {
      const stats = {
        summary: {
          total: attendanceData.length,
          regular: 0,
          sudden: 0
        },
        statusBreakdown: {
          on_time: 0,
          late: 0,
          missed: 0,
          absent: 0,
          excused: 0,
          business_trip: 0,
          pending: 0,
          scheduled: 0
        },
        typeBreakdown: {
          regular: {
            total: 0,
            completed: 0,
            pending: 0,
            exempted: 0
          },
          sudden: {
            total: 0,
            completed: 0,
            pending: 0,
            exempted: 0
          }
        },
        dateRange,
        calculatedAt: Date.now()
      };

      // Phân tích từng item
      attendanceData.forEach(item => {
        const { type, status } = item;

        // Count by type
        if (type === 'regular') {
          stats.summary.regular++;
          stats.typeBreakdown.regular.total++;

          if (['on_time', 'late'].includes(status)) {
            stats.typeBreakdown.regular.completed++;
          } else if (['excused', 'business_trip'].includes(status)) {
            stats.typeBreakdown.regular.exempted++;
          } else {
            stats.typeBreakdown.regular.pending++;
          }
        } else if (type === 'sudden') {
          stats.summary.sudden++;
          stats.typeBreakdown.sudden.total++;

          if (['on_time', 'late'].includes(status)) {
            stats.typeBreakdown.sudden.completed++;
          } else if (['excused', 'business_trip'].includes(status)) {
            stats.typeBreakdown.sudden.exempted++;
          } else {
            stats.typeBreakdown.sudden.pending++;
          }
        }

        // Count by status
        if (stats.statusBreakdown.hasOwnProperty(status)) {
          stats.statusBreakdown[status]++;
        }
      });

      // Tính toán tỷ lệ
      stats.rates = {
        completionRate: stats.summary.total > 0
          ? Math.round(((stats.statusBreakdown.on_time + stats.statusBreakdown.late) / stats.summary.total) * 100)
          : 0,
        onTimeRate: stats.summary.total > 0
          ? Math.round((stats.statusBreakdown.on_time / stats.summary.total) * 100)
          : 0,
        exemptionRate: stats.summary.total > 0
          ? Math.round(((stats.statusBreakdown.excused + stats.statusBreakdown.business_trip) / stats.summary.total) * 100)
          : 0
      };

      return stats;

    } catch (error) {
      console.error('[AttendanceOverview] Error calculating real-time statistics:', error);
      return this.calculateBasicStatistics(attendanceData);
    }
  }

  /**
   * Tính toán statistics cơ bản (fallback)
   * @param {Array} attendanceData - Dữ liệu điểm danh
   * @returns {Object} Basic statistics
   */
  calculateBasicStatistics(attendanceData) {
    const total = attendanceData.length;
    const regular = attendanceData.filter(item => item.type === 'regular').length;
    const sudden = attendanceData.filter(item => item.type === 'sudden').length;

    return {
      summary: { total, regular, sudden },
      statusBreakdown: {},
      typeBreakdown: {},
      rates: { completionRate: 0, onTimeRate: 0, exemptionRate: 0 },
      calculatedAt: Date.now(),
      isBasic: true
    };
  }

  /**
   * Cập nhật method getAttendanceStatusOverview để sử dụng options mới
   * @param {String} userId - ID cán bộ
   * @param {Object} filters - Bộ lọc
   * @param {Object} processingOptions - Tùy chọn xử lý
   * @returns {Object} Tổng quan trạng thái điểm danh
   */
  async getAttendanceStatusOverviewV2(userId, filters = {}, processingOptions = {}) {
    try {
      const {
        date,
        startDate,
        endDate,
        period = 'day'
      } = filters;

      // Xác định khoảng thời gian
      const dateRange = this.calculateDateRange(date, startDate, endDate, period);

      // Lấy dữ liệu song song
      const [regularAttendance, suddenAttendance] = await Promise.all([
        this.getRegularAttendanceData(userId, dateRange),
        this.getSuddenAttendanceData(userId, dateRange)
      ]);

      // Tổng hợp dữ liệu với options mới
      const overview = this.combineAttendanceData(
        regularAttendance,
        suddenAttendance,
        dateRange,
        processingOptions
      );

      return {
        success: true,
        data: overview
      };

    } catch (error) {
      console.error('Error getting attendance status overview V2:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new AttendanceOverviewService();
