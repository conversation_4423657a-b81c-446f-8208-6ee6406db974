/**
 * AttendanceDataValidator - Component validate và fix data inconsistency
 * Kiểm tra tính nhất quán của dữ liệu điểm danh và tự động sửa lỗi
 * Đảm bảo data integrity cho hệ thống điểm danh
 */

const DateUtils = require('../utils/dateUtils');

class AttendanceDataValidator {
  constructor() {
    // Định nghĩa các loại validation rules
    this.VALIDATION_RULES = {
      REQUIRED_FIELDS: 'required_fields',
      DATE_FORMAT: 'date_format',
      TIME_LOGIC: 'time_logic',
      STATUS_CONSISTENCY: 'status_consistency',
      DUPLICATE_CHECK: 'duplicate_check',
      BUSINESS_LOGIC: 'business_logic'
    };

    // Danh sách trạng thái hợp lệ
    this.VALID_STATUSES = [
      'on_time', 'late', 'missed', 'absent', 
      'excused', 'business_trip', 'pending', 'scheduled'
    ];

    // Required fields cho mỗi loại attendance
    this.REQUIRED_FIELDS = {
      regular: ['type', 'date', 'shift', 'startTime', 'status'],
      sudden: ['type', 'date', 'startTime', 'status', 'sessionId']
    };
  }

  /**
   * Validate và fix dữ liệu điểm danh
   * @param {Array} attendanceData - Dữ liệu cần validate
   * @param {Object} options - Tùy chọn validation
   * @returns {Object} Kết quả validation và dữ liệu đã fix
   */
  validateAndFix(attendanceData, options = {}) {
    const {
      autoFix = true,
      strictMode = false,
      validationRules = Object.values(this.VALIDATION_RULES)
    } = options;

    try {
      const validationResult = {
        isValid: true,
        errors: [],
        warnings: [],
        fixedItems: [],
        validData: [],
        invalidData: [],
        statistics: {
          total: attendanceData.length,
          valid: 0,
          fixed: 0,
          invalid: 0
        }
      };

      // Validate từng item
      attendanceData.forEach((item, index) => {
        const itemResult = this._validateItem(item, validationRules, strictMode);
        
        if (itemResult.isValid) {
          validationResult.validData.push(item);
          validationResult.statistics.valid++;
        } else if (autoFix && itemResult.canFix) {
          const fixedItem = this._fixItem(item, itemResult.errors);
          if (fixedItem) {
            validationResult.validData.push(fixedItem);
            validationResult.fixedItems.push({
              index,
              original: item,
              fixed: fixedItem,
              fixes: itemResult.errors
            });
            validationResult.statistics.fixed++;
          } else {
            validationResult.invalidData.push({ index, item, errors: itemResult.errors });
            validationResult.statistics.invalid++;
          }
        } else {
          validationResult.invalidData.push({ index, item, errors: itemResult.errors });
          validationResult.statistics.invalid++;
        }

        // Collect errors and warnings
        validationResult.errors.push(...itemResult.errors);
        validationResult.warnings.push(...itemResult.warnings);
      });

      // Kiểm tra duplicate sau khi validate individual items
      if (validationRules.includes(this.VALIDATION_RULES.DUPLICATE_CHECK)) {
        const duplicateResult = this._checkDuplicates(validationResult.validData);
        validationResult.errors.push(...duplicateResult.errors);
        validationResult.warnings.push(...duplicateResult.warnings);
      }

      // Determine overall validity
      validationResult.isValid = validationResult.errors.length === 0;

      return validationResult;

    } catch (error) {
      console.error('[AttendanceDataValidator] Error in validateAndFix:', error);
      return {
        isValid: false,
        errors: [{ type: 'system_error', message: error.message }],
        warnings: [],
        validData: attendanceData, // Fallback to original data
        invalidData: [],
        fixedItems: [],
        statistics: { total: attendanceData.length, valid: 0, fixed: 0, invalid: 0 }
      };
    }
  }

  /**
   * Validate một item cụ thể
   * @private
   */
  _validateItem(item, validationRules, strictMode) {
    const result = {
      isValid: true,
      canFix: true,
      errors: [],
      warnings: []
    };

    // Required fields validation
    if (validationRules.includes(this.VALIDATION_RULES.REQUIRED_FIELDS)) {
      const requiredFieldsResult = this._validateRequiredFields(item);
      result.errors.push(...requiredFieldsResult.errors);
      result.warnings.push(...requiredFieldsResult.warnings);
      if (!requiredFieldsResult.canFix) result.canFix = false;
    }

    // Date format validation
    if (validationRules.includes(this.VALIDATION_RULES.DATE_FORMAT)) {
      const dateFormatResult = this._validateDateFormat(item);
      result.errors.push(...dateFormatResult.errors);
      result.warnings.push(...dateFormatResult.warnings);
    }

    // Time logic validation
    if (validationRules.includes(this.VALIDATION_RULES.TIME_LOGIC)) {
      const timeLogicResult = this._validateTimeLogic(item);
      result.errors.push(...timeLogicResult.errors);
      result.warnings.push(...timeLogicResult.warnings);
    }

    // Status consistency validation
    if (validationRules.includes(this.VALIDATION_RULES.STATUS_CONSISTENCY)) {
      const statusResult = this._validateStatusConsistency(item);
      result.errors.push(...statusResult.errors);
      result.warnings.push(...statusResult.warnings);
    }

    // Business logic validation
    if (validationRules.includes(this.VALIDATION_RULES.BUSINESS_LOGIC)) {
      const businessLogicResult = this._validateBusinessLogic(item, strictMode);
      result.errors.push(...businessLogicResult.errors);
      result.warnings.push(...businessLogicResult.warnings);
    }

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * Validate required fields
   * @private
   */
  _validateRequiredFields(item) {
    const result = { errors: [], warnings: [], canFix: true };
    const requiredFields = this.REQUIRED_FIELDS[item.type] || this.REQUIRED_FIELDS.regular;

    requiredFields.forEach(field => {
      if (!item.hasOwnProperty(field) || item[field] === null || item[field] === undefined) {
        result.errors.push({
          type: this.VALIDATION_RULES.REQUIRED_FIELDS,
          field,
          message: `Thiếu trường bắt buộc: ${field}`,
          fixable: this._canFixMissingField(field)
        });
        
        if (!this._canFixMissingField(field)) {
          result.canFix = false;
        }
      }
    });

    return result;
  }

  /**
   * Validate date format
   * @private
   */
  _validateDateFormat(item) {
    const result = { errors: [], warnings: [] };

    if (item.date && typeof item.date === 'string') {
      // Kiểm tra format DD-MM-YYYY
      if (!/^\d{2}-\d{2}-\d{4}$/.test(item.date)) {
        result.errors.push({
          type: this.VALIDATION_RULES.DATE_FORMAT,
          field: 'date',
          message: `Định dạng ngày không hợp lệ: ${item.date}. Yêu cầu DD-MM-YYYY`,
          fixable: true
        });
      }
    }

    return result;
  }

  /**
   * Validate time logic
   * @private
   */
  _validateTimeLogic(item) {
    const result = { errors: [], warnings: [] };

    // Kiểm tra startTime hợp lệ
    if (item.startTime && typeof item.startTime === 'number') {
      if (item.startTime < 0 || item.startTime > Date.now() + (365 * 24 * 60 * 60 * 1000)) {
        result.errors.push({
          type: this.VALIDATION_RULES.TIME_LOGIC,
          field: 'startTime',
          message: `Thời gian bắt đầu không hợp lệ: ${item.startTime}`,
          fixable: false
        });
      }
    }

    // Kiểm tra checkinTime nếu có
    if (item.checkinTime && typeof item.checkinTime === 'number') {
      if (item.checkinTime < 0 || item.checkinTime > Date.now()) {
        result.warnings.push({
          type: this.VALIDATION_RULES.TIME_LOGIC,
          field: 'checkinTime',
          message: `Thời gian điểm danh có vẻ không hợp lệ: ${item.checkinTime}`
        });
      }

      // Kiểm tra logic: checkinTime phải sau startTime
      if (item.startTime && item.checkinTime < item.startTime) {
        result.errors.push({
          type: this.VALIDATION_RULES.TIME_LOGIC,
          field: 'checkinTime',
          message: 'Thời gian điểm danh không thể trước thời gian bắt đầu',
          fixable: true
        });
      }
    }

    return result;
  }

  /**
   * Validate status consistency
   * @private
   */
  _validateStatusConsistency(item) {
    const result = { errors: [], warnings: [] };

    // Kiểm tra status có trong danh sách hợp lệ
    if (item.status && !this.VALID_STATUSES.includes(item.status)) {
      result.errors.push({
        type: this.VALIDATION_RULES.STATUS_CONSISTENCY,
        field: 'status',
        message: `Trạng thái không hợp lệ: ${item.status}`,
        fixable: true
      });
    }

    // Kiểm tra logic: nếu có checkinTime thì status phải là on_time hoặc late
    if (item.checkinTime && !['on_time', 'late'].includes(item.status)) {
      result.warnings.push({
        type: this.VALIDATION_RULES.STATUS_CONSISTENCY,
        field: 'status',
        message: `Có thời gian điểm danh nhưng trạng thái là: ${item.status}`
      });
    }

    return result;
  }

  /**
   * Validate business logic
   * @private
   */
  _validateBusinessLogic(item, strictMode) {
    const result = { errors: [], warnings: [] };

    // Kiểm tra logic nghiệp vụ cụ thể
    if (item.type === 'regular') {
      // Regular attendance phải có shift
      if (!item.shift || !['morning', 'afternoon'].includes(item.shift)) {
        result.errors.push({
          type: this.VALIDATION_RULES.BUSINESS_LOGIC,
          field: 'shift',
          message: `Ca làm việc không hợp lệ: ${item.shift}`,
          fixable: true
        });
      }
    } else if (item.type === 'sudden') {
      // Sudden attendance phải có sessionId
      if (!item.sessionId) {
        result.errors.push({
          type: this.VALIDATION_RULES.BUSINESS_LOGIC,
          field: 'sessionId',
          message: 'Thiếu sessionId cho điểm danh đột xuất',
          fixable: false
        });
      }
    }

    return result;
  }

  /**
   * Kiểm tra duplicate
   * @private
   */
  _checkDuplicates(validData) {
    const result = { errors: [], warnings: [] };
    const seen = new Set();

    validData.forEach((item, index) => {
      const key = this._generateDuplicateKey(item);
      if (seen.has(key)) {
        result.warnings.push({
          type: this.VALIDATION_RULES.DUPLICATE_CHECK,
          index,
          message: `Phát hiện dữ liệu trùng lặp: ${key}`,
          item
        });
      } else {
        seen.add(key);
      }
    });

    return result;
  }

  /**
   * Tạo key để check duplicate
   * @private
   */
  _generateDuplicateKey(item) {
    if (item.type === 'regular') {
      return `${item.type}_${item.date}_${item.shift}`;
    } else if (item.type === 'sudden') {
      return `${item.type}_${item.sessionId}`;
    }
    return `${item.type}_${item.date}_${item.startTime}`;
  }

  /**
   * Fix một item có lỗi
   * @private
   */
  _fixItem(item, errors) {
    const fixedItem = { ...item };

    errors.forEach(error => {
      if (error.fixable) {
        switch (error.type) {
          case this.VALIDATION_RULES.REQUIRED_FIELDS:
            this._fixMissingField(fixedItem, error.field);
            break;
          case this.VALIDATION_RULES.DATE_FORMAT:
            this._fixDateFormat(fixedItem);
            break;
          case this.VALIDATION_RULES.TIME_LOGIC:
            this._fixTimeLogic(fixedItem, error.field);
            break;
          case this.VALIDATION_RULES.STATUS_CONSISTENCY:
            this._fixStatusConsistency(fixedItem);
            break;
          case this.VALIDATION_RULES.BUSINESS_LOGIC:
            this._fixBusinessLogic(fixedItem, error.field);
            break;
        }
      }
    });

    return fixedItem;
  }

  /**
   * Fix missing field
   * @private
   */
  _fixMissingField(item, field) {
    switch (field) {
      case 'status':
        item.status = 'pending';
        break;
      case 'type':
        item.type = 'regular';
        break;
      case 'shift':
        // Guess shift based on startTime
        if (item.startTime) {
          const hour = new Date(item.startTime).getHours();
          item.shift = hour < 12 ? 'morning' : 'afternoon';
        } else {
          item.shift = 'morning';
        }
        break;
    }
  }

  /**
   * Fix date format
   * @private
   */
  _fixDateFormat(item) {
    // Attempt to parse and reformat date
    try {
      const date = new Date(item.date);
      if (!isNaN(date.getTime())) {
        item.date = DateUtils.convertYYYYMMDDtoDDMMYYYY(
          date.toISOString().split('T')[0]
        );
      }
    } catch (error) {
      // If can't fix, set to current date
      item.date = DateUtils.getCurrentDateDDMMYYYY();
    }
  }

  /**
   * Fix time logic
   * @private
   */
  _fixTimeLogic(item, field) {
    if (field === 'checkinTime' && item.startTime) {
      // Set checkinTime to startTime if invalid
      item.checkinTime = item.startTime;
    }
  }

  /**
   * Fix status consistency
   * @private
   */
  _fixStatusConsistency(item) {
    if (!this.VALID_STATUSES.includes(item.status)) {
      item.status = 'pending';
    }
  }

  /**
   * Fix business logic
   * @private
   */
  _fixBusinessLogic(item, field) {
    if (field === 'shift' && item.type === 'regular') {
      item.shift = 'morning'; // Default fallback
    }
  }

  /**
   * Kiểm tra field có thể fix được không
   * @private
   */
  _canFixMissingField(field) {
    const fixableFields = ['status', 'type', 'shift'];
    return fixableFields.includes(field);
  }

  /**
   * Lấy báo cáo validation summary
   * @param {Object} validationResult - Kết quả validation
   * @returns {Object} Summary report
   */
  generateValidationReport(validationResult) {
    const { statistics, errors, warnings, fixedItems } = validationResult;
    
    return {
      summary: {
        totalItems: statistics.total,
        validItems: statistics.valid,
        fixedItems: statistics.fixed,
        invalidItems: statistics.invalid,
        successRate: statistics.total > 0 
          ? Math.round(((statistics.valid + statistics.fixed) / statistics.total) * 100) 
          : 0
      },
      errorBreakdown: this._categorizeIssues(errors),
      warningBreakdown: this._categorizeIssues(warnings),
      fixSummary: this._summarizeFixes(fixedItems),
      recommendations: this._generateRecommendations(validationResult)
    };
  }

  /**
   * Categorize issues by type
   * @private
   */
  _categorizeIssues(issues) {
    const breakdown = {};
    issues.forEach(issue => {
      const type = issue.type || 'unknown';
      if (!breakdown[type]) {
        breakdown[type] = 0;
      }
      breakdown[type]++;
    });
    return breakdown;
  }

  /**
   * Summarize fixes applied
   * @private
   */
  _summarizeFixes(fixedItems) {
    const summary = {};
    fixedItems.forEach(item => {
      item.fixes.forEach(fix => {
        const type = fix.type || 'unknown';
        if (!summary[type]) {
          summary[type] = 0;
        }
        summary[type]++;
      });
    });
    return summary;
  }

  /**
   * Generate recommendations
   * @private
   */
  _generateRecommendations(validationResult) {
    const recommendations = [];
    
    if (validationResult.statistics.invalid > 0) {
      recommendations.push('Có dữ liệu không thể tự động sửa, cần kiểm tra thủ công');
    }
    
    if (validationResult.warnings.length > 0) {
      recommendations.push('Có cảnh báo về tính nhất quán dữ liệu, nên xem xét');
    }
    
    if (validationResult.statistics.fixed > 0) {
      recommendations.push('Đã tự động sửa một số lỗi, nên review kết quả');
    }
    
    return recommendations;
  }
}

module.exports = AttendanceDataValidator;
